# utils/embedding_utils.py
from fastembed import SparseTextEmbedding
from langchain_openai import AzureOpenAIEmbeddings
import os
from dotenv import load_dotenv

load_dotenv()
AZURE_OPENAI_DEPLOYMENT_NAME_EMBEDDINGS = os.getenv("AZURE_OPENAI_DEPLOYMENT_NAME_EMBEDDINGS")

# Initialize embedding models (once, globally)
sparse_embedder = SparseTextEmbedding(model_name="Qdrant/bm42-all-minilm-l6-v2-attentions")
dense_embedder = AzureOpenAIEmbeddings(model=AZURE_OPENAI_DEPLOYMENT_NAME_EMBEDDINGS)


def get_embeddings(text):
    sparse_embedding = list(sparse_embedder.query_embed(text))[0]
    dense_embedding = dense_embedder.embed_query(text)
    #print("sparse_embedding:", sparse_embedding)
    #print("dense_embedding:", dense_embedding[:10])
    return sparse_embedding, dense_embedding

def rerank_candidates():
    pass
def explain_selection():
    pass

import time 
from langchain_openai import AzureOpenAIEmbeddings
azure_endpoint = "https://openai-smarthr.openai.azure.com/openai/deployments/text-embedding-3-small-smarthr/embeddings?api-version=2023-05-15"
api_key="********************************"
model_openai = AzureOpenAIEmbeddings(azure_endpoint=azure_endpoint, api_key=api_key)
def generate_openai_embedding(text):
    max_retries = 3
    retries = 0
    while retries < max_retries:
        try:
            # Generate embedding
            embedding = model_openai.embed_documents([text])[0]
            return embedding
        except Exception as e:
            retries += 1
            print(f"Error generating embedding for text: {text[:30]}...: {e}")
            
            # If retries exceed max_retries, log and return None to continue processing
            if retries >= max_retries:
                print(f"Max retries exceeded for embedding generation. Skipping...")
                return None