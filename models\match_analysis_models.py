from datetime import datetime
from typing import Any, Dict, Optional, List

from pydantic import BaseModel, Field, ConfigDict
from typing_extensions import Annotated


class CandidatePositionAnalysis(BaseModel):
    LLM_Analysis: Dict[str, Any] = Field(
        ..., description="Analysis results from the LLM"
    )
    extra_questions: Dict[str, Any] = Field(
        ..., description="Additional clarifications or interview questions suggested"
    )
    highlights: Dict[str, Any] = Field(
        ...,
        description="Key points or standout features of the candidate for this position",
    )
    Score: float = Field(..., description="Overall score assigned by the LLM.")
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)


# class SkillMatchAnalysis(BaseModel):
#     coef_match: float = Field(..., description="Coefficient indicating the degree of skill match.")
#     comment: str = Field(..., description="Commentary on the skill match.")
#     model_config = ConfigDict(populate_by_name=True) 

class LLMAnalysis(BaseModel):
    reason: str = Field(..., description="Explanation of the analysis.")
    skill_match_analysis: Dict[str, str] = Field(
        ..., description="Analysis of skill matches, keyed by skill name. You can just cite matched skills, not all skills. You can not repeat the not matched skills."
    )
    skill_not_matched: List[str] = Field(..., description="List of skills not matched. You can not repeat the matched skills.")

    model_config = ConfigDict(extra="allow")


class PositionCandidateAnalysis(BaseModel):
    LLM_Analysis: LLMAnalysis = Field(..., description="Analysis provided by the LLM.")
    extra_questions: List[str] = Field(
        ..., description="Additional questions generated by the LLM."
    )
    highlights: List[str] = Field(
        ..., description="Key points highlighted in the analysis."
    )
    Score: float = Field(..., description="Overall score assigned by the LLM. This is a value between 0 and 100.")


class BatchMatchAnalysis(BaseModel):
    candidates_analysis: List[PositionCandidateAnalysis] = Field(
        ..., description="List of analysis results for multiple candidates"
    )
# summary: Dict[str, Any] = Field(
#     ..., description="Overall summary of the batch analysis"
# )
# created_at: datetime = Field(default_factory=datetime.now)
# updated_at: datetime = Field(default_factory=datetime.now)


class CompatibilityEvaluation(BaseModel):
    compatibilityPercentage: float = Field(..., description="Percentage of compatibility with the position (based on skills, experience, education, and other key requirements). This is a percentage between 0.0 and 100.0. Example: 85.5, 45.1, 99.0")
    recommendation: bool = Field(..., description="Recommendation: Indicate whether or not the candidate is recommended to move forward in the process, along with a brief justification.")
    justification: str = Field(..., description="Justification for the recommendation")
    matchesFound: List[str] = Field(..., description="Matches Found: List in a clear and orderly manner the points on which the candidate meets the requirements in the Job Description.")
    missing: List[str] = Field(..., description="Missing: Indicate the key requirements or competencies from the Job Description that do not appear or are not evident in the candidate's CV.")
