# SmartHR Backend - Documentación Técnica Completa

## 📋 Índice
1. [Visión General del Proyecto](#visión-general-del-proyecto)
2. [Arquitectura del Sistema](#arquitectura-del-sistema)
3. [Microservicios y Componentes](#microservicios-y-componentes)
4. [Proceso de Vectorización](#proceso-de-vectorización)
5. [C<PERSON>lculo de Similitud](#cálculo-de-similitud)
6. [Base de Datos](#base-de-datos)
7. [APIs y Endpoints](#apis-y-endpoints)
8. [Despliegue](#despliegue)

## 🎯 Visión General del Proyecto

SmartHR Backend es un sistema de matching semántico inteligente que utiliza técnicas avanzadas de procesamiento de lenguaje natural (NLP) y embeddings vectoriales para emparejar candidatos con posiciones laborales de manera eficiente y precisa.

### Características Principales:
- **Matching Semántico**: Utiliza embeddings densos y dispersos para encontrar similitudes semánticas
- **Análisis con LLM**: Integración con modelos de lenguaje grandes (GPT-4, Groq) para análisis detallado
- **Base de Datos Vectorial**: Soporte para PostgreSQL con pgvector y Qdrant
- **API RESTful**: Endpoints completos para gestión de candidatos, posiciones, entrevistas y proyectos
- **Monitoreo**: Integración con Azure Application Insights y OpenTelemetry

### Stack Tecnológico:
- **Backend**: FastAPI (Python)
- **Base de Datos**: PostgreSQL con extensión pgvector
- **Vector DB**: Qdrant para búsqueda híbrida
- **LLM**: Azure OpenAI (GPT-4), Groq
- **Embeddings**: OpenAI text-embedding-3-small, BM42
- **Monitoreo**: Azure Application Insights
- **Contenedores**: Docker

## 🏗️ Arquitectura del Sistema

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   FastAPI       │    │   PostgreSQL    │
│   (Cliente)     │◄──►│   Backend       │◄──►│   + pgvector    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌─────────────────┐
                       │     Qdrant      │
                       │  Vector Store   │
                       └─────────────────┘
                              │
                              ▼
                       ┌─────────────────┐
                       │   Azure OpenAI  │
                       │   + Groq LLMs   │
                       └─────────────────┘
```

### Flujo de Datos Principal:
1. **Ingesta**: CVs y descripciones de posiciones se procesan y limpian
2. **Vectorización**: Texto se convierte en embeddings densos y dispersos
3. **Almacenamiento**: Embeddings se guardan en PostgreSQL y Qdrant
4. **Matching**: Búsqueda por similitud coseno para encontrar coincidencias
5. **Análisis LLM**: Evaluación detallada con modelos de lenguaje
6. **Resultados**: Scores y análisis estructurado devuelto al cliente

## 🔧 Microservicios y Componentes

### 1. **Servicio de Candidatos** (`/candidate`)
**Controlador**: `controllers/candidates_controller.py`
**Rutas**: `routes/routes_candidate.py`

**Funcionalidades**:
- Creación y gestión de candidatos
- Procesamiento de CVs (PDF, DOCX)
- Generación de embeddings para candidatos
- Búsqueda y filtrado de candidatos
- Análisis de compatibilidad con posiciones

**Endpoints Principales**:
- `POST /candidate/` - Crear candidato
- `GET /candidate/{id}` - Obtener candidato
- `PUT /candidate/{id}` - Actualizar candidato
- `DELETE /candidate/{id}` - Eliminar candidato
- `POST /candidate/upload` - Subir CV

### 2. **Servicio de Posiciones** (`/position`)
**Controlador**: `controllers/positions_controller.py`
**Rutas**: `routes/routes_position.py`

**Funcionalidades**:
- Gestión de posiciones laborales
- Extracción estructurada de información de posiciones
- Generación de embeddings para posiciones
- Análisis de habilidades requeridas
- Matching con candidatos

**Endpoints Principales**:
- `POST /position/` - Crear posición
- `GET /position/{id}` - Obtener posición
- `PUT /position/{id}` - Actualizar posición
- `POST /position/raw` - Crear desde texto sin procesar

### 3. **Servicio de Entrevistas** (`/interview`)
**Controlador**: `controllers/interview_controller.py`
**Rutas**: `routes/routes_interview.py`

**Funcionalidades**:
- Gestión de entrevistas
- Seguimiento de feedback
- Historial de interacciones candidato-posición

### 4. **Servicio de Proyectos** (`/project`)
**Controlador**: `controllers/project_controller.py`
**Rutas**: `routes/routes_project.py`

**Funcionalidades**:
- Gestión de proyectos
- Agrupación de posiciones por proyecto
- Métricas y reportes por proyecto

### 5. **Servicio de Notas** (`/note`)
**Controlador**: `controllers/notes_controller.py`
**Rutas**: `routes/routes_note.py`

**Funcionalidades**:
- Gestión de notas y comentarios
- Anotaciones sobre candidatos y posiciones

## 🧠 Proceso de Vectorización

### Arquitectura de Embeddings

El sistema utiliza **embeddings densos** para la búsqueda semántica:

#### **Embeddings Densos** (Dense Embeddings)
- **Modelo**: Azure OpenAI `text-embedding-3-small`
- **Dimensiones**: 1536
- **Propósito**: Captura similitud semántica profunda entre candidatos y posiciones
- **Uso**: Búsqueda semántica principal mediante similitud coseno

### Flujo de Vectorización para Candidatos

```python
# 1. Procesamiento del CV
def create_candidate(candidate_data: CandidateCreate):
    # Preparar texto para embedding
    embed_text = prepare_candidate_for_embedding(candidate_data.candidate_info)
    
    # Generar embedding denso
    embedding = generate_openai_embedding(embed_text)
    
    # Almacenar en PostgreSQL
    embedding_vector = format_vector(embedding)
```

#### Proceso Detallado:

1. **Extracción de Texto**: 
   - CVs en PDF/DOCX se convierten a texto plano
   - Se utiliza OCR si es necesario

2. **Limpieza y Estructuración**:
   - Eliminación de caracteres especiales
   - Normalización de formato
   - Extracción de información relevante (habilidades, experiencia, educación)

3. **Preparación para Embedding**:
```python
def prepare_candidate_for_embedding(candidate_info: dict) -> str:
    """
    Extrae información profesional relevante y no-PII
    para generar un texto optimizado para embedding
    """
    # Formato estructurado que incluye:
    # - Resumen profesional
    # - Habilidades técnicas
    # - Experiencia laboral
    # - Educación
    # - Certificaciones
```

4. **Generación de Embeddings**:
```python
def generate_openai_embedding(text):
    """
    Genera embedding denso usando Azure OpenAI
    """
    embedding = model_openai.embed_documents([text])[0]
    return embedding
```

### Flujo de Vectorización para Posiciones

```python
def create_position(position_data: PositionCreate):
    # 1. Transformar a JSON estructurado
    position_info = transform_position_to_json(position_raw_text)
    
    # 2. Preparar texto para embedding
    embed_text = transform_position_to_embedeed_text(position_raw_text)
    
    # 3. Generar embedding
    embedding = generate_openai_embedding(embed_text)
    
    # 4. Extraer habilidades prioritarias
    priority_skills = extract_position_skills(position_raw_text)
```

#### Proceso Detallado:

1. **Estructuración con LLM**:
   - Conversión de texto libre a JSON estructurado
   - Extracción de campos específicos (título, responsabilidades, habilidades)

2. **Optimización para Embedding**:
   - Limpieza y separación de contenido relevante
   - Enfoque en aspectos técnicos y profesionales

3. **Extracción de Habilidades**:
   - Identificación automática de skills técnicos
   - Categorización por nivel de importancia

## 📊 Cálculo de Similitud

### Métrica Principal: Similitud Coseno

La similitud entre candidatos y posiciones se calcula usando **similitud coseno** en el espacio vectorial:

```sql
-- Búsqueda de candidatos similares a una posición
SELECT 
    id, 
    candidate_info, 
    1 - (embedding <=> %s) AS cosine_similarity
FROM candidates_smarthr
WHERE is_deleted = false AND is_active = true
ORDER BY cosine_similarity DESC
LIMIT 5
```

### Fórmula de Similitud Coseno:
```
similitud = 1 - distancia_coseno
similitud = cos(θ) = (A · B) / (||A|| × ||B||)
```

Donde:
- `A` = vector embedding del candidato
- `B` = vector embedding de la posición
- `θ` = ángulo entre los vectores

### Búsqueda Vectorial Principal (PostgreSQL)

La búsqueda principal se realiza directamente en PostgreSQL usando la extensión **pgvector**:

```sql
-- Búsqueda de candidatos más similares a una posición
SELECT
    id,
    candidate_info,
    1 - (embedding <=> %s) AS cosine_similarity
FROM candidates_smarthr
WHERE is_deleted = false AND is_active = true
ORDER BY cosine_similarity DESC
LIMIT 5
```

**Ventajas de pgvector**:
- Búsqueda vectorial nativa en PostgreSQL
- Índices optimizados (ivfflat) para consultas rápidas
- Integración directa con datos relacionales

### Análisis con LLM

Después de la búsqueda vectorial, se realiza un **análisis detallado con LLM**:

```python
def evaluate_candidate(candidate: str, position: str) -> dict:
    """
    Evalúa la compatibilidad candidato-posición usando LLM
    Retorna análisis estructurado con score de 0-100
    """
    system_prompt = get_candidate_analysis_prompt()
    
    analysis_response = inference_with_fallback(
        task_prompt=system_prompt,
        model_schema=PositionCandidateAnalysis,
        user_messages=[
            {"role": "user", "content": f"Candidate: {candidate}"},
            {"role": "user", "content": f"Position: {position}"}
        ],
        models_order=["gpt-4o", "gpt-4o-mini", "groq"]
    )
    
    return analysis_response
```

### Estructura del Score Final:

```python
class PositionCandidateAnalysis(BaseModel):
    LLM_Analysis: dict  # Análisis detallado
    extra_questions: dict  # Preguntas adicionales
    highlights: dict  # Puntos destacados
    Score: float  # Score final 0-100
```

El **score final** combina:
1. **Similitud vectorial** (0-1) × 100
2. **Análisis LLM** (0-100)
3. **Factores de ajuste** (experiencia, habilidades específicas)

## 🗄️ Base de Datos

### Esquema PostgreSQL con pgvector

#### Tabla: `candidates_smarthr`
```sql
CREATE TABLE candidates_smarthr (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    proj_id UUID REFERENCES projects(id),
    candidate_info JSONB,                    -- Información estructurada del candidato
    suggested_positions JSONB,               -- Posiciones sugeridas
    analysis_status TEXT,                    -- Estado del análisis
    to_be_embebbed TEXT,                    -- Texto preparado para embedding
    embedding VECTOR(1536),                 -- Vector embedding (OpenAI)
    Reason_Info JSONB,                      -- Información adicional
    is_active BOOLEAN DEFAULT true,
    is_deleted BOOLEAN DEFAULT false,
    last_matching TIMESTAMP,                -- Última vez que se ejecutó matching
    created_at TIMESTAMP DEFAULT NOW(),
    created_by TEXT,
    updated_at TIMESTAMP DEFAULT NOW(),
    updated_by TEXT
);
```

#### Tabla: `positions_smarthr`
```sql
CREATE TABLE positions_smarthr (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    proj_id UUID REFERENCES projects(id),
    position_info JSONB,                    -- Información estructurada de la posición
    top_candidates JSONB,                   -- Top candidatos encontrados
    to_be_embebbed TEXT,                   -- Texto preparado para embedding
    embedding VECTOR(1536),                -- Vector embedding (OpenAI)
    last_matching TIMESTAMP,               -- Última vez que se ejecutó matching
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    priority_skills JSONB,                 -- Habilidades prioritarias extraídas
    external_id TEXT                       -- ID externo si aplica
);
```

#### Tabla: `projects`
```sql
CREATE TABLE projects (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    project_name TEXT NOT NULL,
    client_name TEXT,
    description TEXT,
    status TEXT DEFAULT 'active',
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
```

#### Tabla: `interviews`
```sql
CREATE TABLE interviews (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    candidate_id UUID REFERENCES candidates_smarthr(id),
    position_id UUID REFERENCES positions_smarthr(id),
    interview_date TIMESTAMP,
    status TEXT,
    feedback JSONB,
    score DECIMAL(5,2),
    notes TEXT,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
```

### Índices para Optimización

```sql
-- Índice para búsqueda vectorial eficiente
CREATE INDEX ON candidates_smarthr USING ivfflat (embedding vector_cosine_ops);
CREATE INDEX ON positions_smarthr USING ivfflat (embedding vector_cosine_ops);

-- Índices para filtros comunes
CREATE INDEX idx_candidates_active ON candidates_smarthr(is_active, is_deleted);
CREATE INDEX idx_positions_project ON positions_smarthr(proj_id);
CREATE INDEX idx_interviews_candidate_position ON interviews(candidate_id, position_id);
```

### Qdrant Vector Store

#### Colección: `my-hybrid-collection`
```python
# Configuración de la colección (funcionalidad disponible pero no utilizada actualmente)
collection_config = {
    "vectors": {
        "openai": {
            "size": 1536,
            "distance": "Cosine"
        }
    }
}
```

#### Estructura de Puntos:
```python
point_structure = {
    "id": "candidate_full_name",
    "vector": {
        "openai": [dense_embedding]       # Embedding denso OpenAI
    },
    "payload": {
        "formatted_cv": "texto_del_cv",
        "pdf_name": "nombre_archivo.pdf",
        "candidate_info": {...}           # Metadatos adicionales
    }
}
```

**Nota**: Qdrant está configurado pero la búsqueda principal se realiza en PostgreSQL.

## 🌐 APIs y Endpoints

### Endpoint Principal de Matching

#### `POST /match`
**Descripción**: Ejecuta matching entre posiciones y candidatos

**Parámetros**:
- `position_id` (opcional): ID de la posición
- `candidate_id` (opcional): ID del candidato
- `limit` (default: 5): Número de resultados
- `hasFeedback` (default: 2): Filtro por feedback (0=sin, 1=con, 2=ambos)
- `batch_mode` (default: true): Modo de análisis (batch vs paralelo)

**Flujo de Ejecución**:
1. **Validación**: Verificar que se proporcione position_id O candidate_id
2. **Búsqueda Vectorial**: Encontrar candidatos/posiciones similares usando coseno
3. **Análisis LLM**: Evaluar compatibilidad con modelos de lenguaje
4. **Respuesta Estructurada**: Retornar resultados con scores y análisis

**Ejemplo de Respuesta**:
```json
{
  "matches": [
    {
      "candidate_id": "uuid",
      "position_id": "uuid",
      "cosine_similarity": 0.87,
      "llm_analysis": {
        "Score": 85.5,
        "LLM_Analysis": {
          "strengths": ["Python", "Machine Learning"],
          "gaps": ["Kubernetes experience"],
          "overall_fit": "Excellent match"
        },
        "highlights": {
          "technical_skills": "Strong in required technologies",
          "experience_level": "Matches seniority requirements"
        }
      }
    }
  ]
}
```

### Endpoints de Candidatos

#### `POST /candidate/upload`
**Descripción**: Sube y procesa CVs (PDF/DOCX)

**Proceso**:
1. Validar formato de archivo
2. Convertir DOCX a PDF si es necesario
3. Extraer texto usando OCR/parsing
4. Generar embeddings
5. Almacenar en base de datos

#### `GET /candidate/{id}/similar`
**Descripción**: Encuentra candidatos similares

### Endpoints de Posiciones

#### `POST /position/raw`
**Descripción**: Crea posición desde texto sin procesar

**Proceso**:
1. **Estructuración LLM**: Convertir texto libre a JSON
2. **Extracción de Skills**: Identificar habilidades requeridas
3. **Generación de Embedding**: Crear vector para búsqueda
4. **Almacenamiento**: Guardar en base de datos

## 🚀 Despliegue

### Docker Configuration

#### Dockerfile
```dockerfile
FROM python:3.11-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
EXPOSE 8080

CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8080"]
```

#### docker-compose.yml
```yaml
version: "3.11"
services:
  smarthr:
    build: .
    ports:
      - "8080:8080"
    environment:
      - AZURE_OPENAI_ENDPOINT=${AZURE_OPENAI_ENDPOINT}
      - AZURE_OPENAI_API_KEY=${AZURE_OPENAI_API_KEY}
      - POSTGRES_HOST=${POSTGRES_HOST}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - APPLICATIONINSIGHTS_CONNECTION_STRING=${APPLICATIONINSIGHTS_CONNECTION_STRING}
```

### Variables de Entorno Requeridas

```bash
# Azure OpenAI
AZURE_OPENAI_DEPLOYMENT_NAME_EMBEDDINGS=text-embedding-3-small-smarthr
AZURE_OPENAI_ENDPOINT=https://openai-smarthr.openai.azure.com/
AZURE_OPENAI_API_KEY=your_api_key
OPENAI_API_VERSION=2023-05-15

# Base de Datos PostgreSQL
POSTGRES_USER=arroyo_postgres_admin
POSTGRES_PASSWORD=your_password
POSTGRES_HOST=ai-db-poc-server.postgres.database.azure.com
POSTGRES_PORT=5432
POSTGRES_DB=postgres

# Groq (LLM alternativo)
GROQ_API_KEY=your_groq_key

# Monitoreo
APPLICATIONINSIGHTS_CONNECTION_STRING=your_connection_string
LANGCHAIN_TRACING_V2=true
LANGCHAIN_API_KEY=your_langchain_key

# Configuración de Modelos
POSITION_MATCH_MODELS_ORDER=gpt-4o,gpt-4o-mini,groq
```

### Comandos de Despliegue

```bash
# Desarrollo local
docker-compose up --build

# Producción
docker build -t smarthr-backend .
docker run -p 8080:8080 --env-file .env smarthr-backend
```

## 📈 Monitoreo y Observabilidad

### Azure Application Insights
- **Trazas distribuidas** con OpenTelemetry
- **Métricas de rendimiento** de endpoints
- **Logs estructurados** con contexto personalizado
- **Alertas** en tiempo real

### Métricas Clave Monitoreadas:
- Tiempo de respuesta de matching
- Precisión de embeddings
- Uso de tokens LLM
- Errores de base de datos
- Throughput de requests

## 🔧 Notebook y Herramientas de Análisis

### GoldFunction (`notebook/GoldFunction/`)
**Propósito**: Herramientas especializadas para análisis y transformación de datos

#### `position_output_class.py`
- Modelos Pydantic para estructuración de posiciones
- Clases para skills, seniority, allocations
- Validación de datos de entrada

#### `transformation.py`
- Funciones de transformación de datos
- Limpieza y normalización de texto
- Preparación para embeddings

### Notebooks de Análisis:
- `get_position.ipynb`: Análisis de posiciones
- `notebook_aux.ipynb`: Herramientas auxiliares
- `Posiciones.xlsx`: Datos de ejemplo

## 🎯 Casos de Uso Principales

### 1. Matching Candidato → Posiciones
```python
# Buscar las mejores posiciones para un candidato específico
POST /match?candidate_id=uuid&limit=10
```

### 2. Matching Posición → Candidatos
```python
# Encontrar los mejores candidatos para una posición
POST /match?position_id=uuid&limit=5&hasFeedback=0
```

### 3. Análisis Batch
```python
# Analizar múltiples candidatos en una sola llamada LLM
POST /match?position_id=uuid&batch_mode=true
```

### 4. Búsqueda Vectorial Optimizada
```python
# Búsqueda semántica con índices vectoriales
SELECT id, 1 - (embedding <=> %s) AS similarity
FROM candidates_smarthr
ORDER BY similarity DESC LIMIT 10
```

## 🔍 Optimizaciones y Mejores Prácticas

### Performance:
- **Índices vectoriales** (ivfflat) para búsquedas rápidas
- **Caching** de embeddings frecuentes
- **Batch processing** para análisis LLM
- **Conexiones de BD** con pooling

### Precisión:
- **Embeddings semánticos** de alta calidad (OpenAI)
- **Múltiples modelos LLM** con fallback
- **Validación de scores** (0-100 range)
- **Normalización** de texto de entrada

### Escalabilidad:
- **Arquitectura stateless**
- **Separación de concerns** por microservicio
- **Async/await** para operaciones I/O
- **Monitoreo proactivo**

---

## 📞 Soporte y Mantenimiento

Para consultas técnicas o reportar issues:
- Revisar logs en Azure Application Insights
- Verificar métricas de base de datos
- Validar configuración de variables de entorno
- Comprobar conectividad con servicios externos (OpenAI, Groq)

**Última actualización**: Enero 2025
