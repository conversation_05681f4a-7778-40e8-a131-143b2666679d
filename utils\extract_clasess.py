import ast
from typing import List

def read_py_file_extract_class_to_string(file_path: str = "models/arroyo_models.py") -> str:
    """
    Reads a Python file, extracts all class definitions as strings,
    and removes any import statements.

    Args:
        file_path (str): The path to the Python (.py) file.

    Returns:
        str: A string containing all class definitions from the file.
    """
    with open(file_path, 'r', encoding='utf-8') as file:
        source_lines = file.readlines()
        source = ''.join(source_lines)
    
    # Parse the source code into an AST
    parsed_ast = ast.parse(source)
    
    class_definitions: List[str] = []
    
    for node in parsed_ast.body:
        if isinstance(node, ast.ClassDef):
            # Get the start and end line numbers of the class definition
            start_line = node.lineno - 1  # line numbers start at 1
            # To get the end line, we can use the last node inside the class
            if node.body:
                end_line = node.body[-1].end_lineno
            else:
                end_line = node.lineno
            # Extract the class definition lines
            class_lines = source_lines[start_line:end_line]
            class_str = ''.join(class_lines).strip()
            class_definitions.append(class_str)
    
    # Combine all class definitions into a single string separated by two newlines
    all_classes_str = '\n\n'.join(class_definitions)
    
    return all_classes_str