# import os

# from dotenv import load_dotenv
# from langchain.cache import InMemoryCache
# from langchain.globals import set_llm_cache
# from langchain_groq import ChatGroq
# from langchain_openai import AzureChatOpenAI

# load_dotenv()  # Load environment variables
# GROQ_API_KEY = os.getenv("GROQ_API_KEY")


# class LLM_Model:
#     def __init__(self, name, provider="openai", temperature=0):
#         self.llm = None
#         if provider == "openai":
#             self.llm = AzureChatOpenAI(temperature=temperature, deployment_name=name)
#         elif provider == "groq":
#             self.llm = ChatGroq(
#                 temperature=temperature, model=name, api_key=GROQ_API_KEY
#             )

#     def get_llm(self):
#         return self.llm


# # Set caching
# set_llm_cache(InMemoryCache())

# # Initialize models pool here
# models_pool = {
#     "gpt-4o": LLM_Model("gpt-4o-smarthr", provider="openai").get_llm(),
#     "gpt-4o-mini": LLM_Model("gpt-4o-mini-smarthr", provider="openai").get_llm(),
#     "llama33-70b": LLM_Model("llama-3.3-70b-versatile", provider="grok").get_llm(),
#     "llama4-light": LLM_Model("meta-llama/llama-4-scout-17b-16e-instruct", provider="grok").get_llm(),
#     "llama4-pro": LLM_Model("meta-llama/llama-4-maverick-17b-128e-instruct", provider="grok").get_llm(),
#     "llama32-90b": LLM_Model("llama-3.2-90b-vision-preview", provider="grok").get_llm(),
# }


