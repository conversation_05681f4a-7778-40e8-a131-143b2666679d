# candidates_controller.py
from contextlib import contextmanager
import json
from typing import List, Optional
import psycopg2
from psycopg2.extras import <PERSON><PERSON>, RealDictCursor
from psycopg2 import sql
from fastapi import HTTPException
from models.validate_models import ValidateCandidateItem, ValidateCandidateRequest, ValidateCandidateResponse
from config.config import CANDIDATE_RESUME_PAGE_OPTIONS
from core.config import settings
from models.candidate import Candidate, CandidateCreate, CandidateFilters, CandidateUpdate
from templates.candidates_templates.candidates_arroyo.json_column import (
    prepare_candidate_for_embedding,
)
from utils.utils_embeddings import format_vector, generate_openai_embedding
import os
import pdfkit
from jinja2 import ChainableUndefined, Environment, BaseLoader
import io
from opentelemetry import trace  # NEW
from opentelemetry.propagate import extract  # (Opcional, si quieres extraer contexto de algún header)
from opentelemetry.trace import Span  # (Opcional, si quieres tipar variables)
from opentelemetry.trace.status import Status, StatusCode
from utils import match_evaluations as match_functions 
from utils.match_evaluations import evaluate_candidates_batch
from pdf2docx import Converter
from datetime import datetime, time
# Telemetry Section
import logging
# Configurar el logger de Python (los logs se enviarán a App Insights por OpenTelemetry)
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)
tracer = trace.get_tracer(__name__) 


# DB helper to get cursor
@contextmanager
def get_cursor():
    conn = psycopg2.connect(settings.DATABASE_URL)
    try:
        with conn:
            with conn.cursor() as cur:
                yield cur
    finally:
        conn.close()


# Create a new candidate
# This function creates a new candidate in the database and returns the created candidate object.
def create_candidate(candidate_data: CandidateCreate) -> Candidate:
    try:
        conn = psycopg2.connect(settings.DATABASE_URL)
        cur = conn.cursor()
        # Prepare text to embed
        # Implement get_candidate_embedding_text() as before:
        embed_text = prepare_candidate_for_embedding(candidate_data.candidate_info)
        #### To specialize considering templates
        # print(embed_text)
        embedding = generate_openai_embedding(embed_text)
        embedding_vector = format_vector(embedding) if embedding else None

        cur.execute(
            """
            INSERT INTO candidates_smarthr 
            (proj_id, candidate_info, suggested_positions, analysis_status, to_be_embebbed, embedding, created_by, updated_by, created_at, updated_at)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, NOW(), NOW())
            RETURNING id, proj_id, candidate_info, suggested_positions, analysis_status, last_matching, is_active, reason_info, updated_by, updated_at, created_by, created_at
            """,
            (
                candidate_data.proj_id,
                Json(candidate_data.candidate_info),
                Json(candidate_data.suggested_positions),
                candidate_data.analysis_status,
                embed_text,
                embedding_vector,
                candidate_data.created_by,
                candidate_data.created_by,  # Assuming created_by is the same as updated_by for creation
            ),
        )

        row = cur.fetchone()
        conn.commit()
        cur.close()
        conn.close()

        if not row:
            return None

        return Candidate(
            id=str(row[0]),
            proj_id=str(row[1]),
            candidate_info=row[2],
            suggested_positions=row[3],
            analysis_status=row[4],
            last_matching=row[5],
            is_active=row[6],
            reason_info=row[7],
            updated_by=row[8],
            updated_at=row[9],
            created_by=row[10],
            created_at=row[11]
        )
    except Exception as e:
        print(f"Error occurred while creating candidate: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
    finally:
        if conn:
            conn.close()


# Update an existing candidate
# This function updates an existing candidate in the database and returns the updated candidate object.
def update_candidate(candidate_data: CandidateUpdate) -> Candidate:
    conn = psycopg2.connect(settings.DATABASE_URL)
    cur = conn.cursor()
    # Prepare text to embed
    # Implement get_candidate_embedding_text() as before:
    embed_text = prepare_candidate_for_embedding(candidate_data.candidate_info)
    candidate_embebbed_text = get_candidate_embebbed_text_by_id(candidate_data.id)
    embedding = ""

    if (candidate_embebbed_text != embed_text):
        embedding = generate_openai_embedding(embed_text)
        embedding_vector = format_vector(embedding) if embedding else None

    params = [Json(candidate_data.candidate_info),
              Json(candidate_data.suggested_positions)]

    sqlQuery = """
         UPDATE candidates_smarthr SET
            candidate_info = %s, 
            suggested_positions = %s,
         """

    if (len(embedding) != 0):
        sqlQuery += """
                        to_be_embebbed= %s, 
                        embedding = %s, 
                    """
        params.extend([embed_text, embedding_vector])

    sqlQuery += """
                updated_by = %s,
                updated_at = Now()
                Where id = %s
                RETURNING id, proj_id, candidate_info, suggested_positions, analysis_status, last_matching, is_active, reason_info, updated_by, updated_at, created_by, created_at
            """
    params.append(candidate_data.updated_by)
    params.append(candidate_data.id)

    cur.execute(sqlQuery, params)
    row = cur.fetchone()
    conn.commit()
    cur.close()
    conn.close()

    if not row:
        return None

    return Candidate(
        id=str(row[0]),
        proj_id=str(row[1]),
        candidate_info=row[2],
        suggested_positions=row[3],
        analysis_status=row[4],
        last_matching=row[5],
        is_active=row[6],
        reason_info=row[7],
        updated_by=row[8],
        updated_at=row[9],
        created_by=row[10],
        created_at=row[11]
    )


# Get candidate embedding text by ID
# This function retrieves the text to be embedded for a candidate by its ID.
def get_candidate_embebbed_text_by_id(candidate_id: str) -> Optional[str]:
    conn = psycopg2.connect(settings.DATABASE_URL)
    cur = conn.cursor()
    cur.execute(
        """
        SELECT to_be_embebbed FROM candidates_smarthr WHERE id::text=%s
    """,
        (candidate_id,),
    )
    row = cur.fetchone()
    cur.close()
    conn.close()
    if not row:
        return None

    return str(row[0])


# Get candidate by ID
# This function retrieves a candidate from the database by its ID.
def get_candidate_by_id(candidate_id: str) -> Optional[Candidate]:
    conn = psycopg2.connect(settings.DATABASE_URL)
    cur = conn.cursor()
    cur.execute(
        """
        SELECT id, proj_id, candidate_info, suggested_positions, analysis_status, last_matching, is_active, reason_info, updated_by, updated_at, created_by, created_at
        FROM candidates_smarthr WHERE id::text=%s
    """,
        (candidate_id,),
    )
    row = cur.fetchone()
    cur.close()
    conn.close()

    if not row:
        return None

    return Candidate(
        id=str(row[0]),
        proj_id=str(row[1]),
        candidate_info=row[2],
        suggested_positions=row[3],
        analysis_status=row[4],
        last_matching=row[5],
        is_active=row[6],
        reason_info=row[7],
        updated_by=row[8],
        updated_at=row[9],
        created_by=row[10],
        created_at=row[11]
    )


# Fetch all candidates
# This function retrieves all candidates from the database that are not deleted.
def fetch_all_candidates() -> List[Candidate]:
    conn = psycopg2.connect(settings.DATABASE_URL)
    cur = conn.cursor()
    cur.execute(
        """
        SELECT id, proj_id, candidate_info, suggested_positions, analysis_status, last_matching, is_active, reason_info, updated_by, updated_at, created_by, created_at
        FROM candidates_smarthr WHERE is_deleted = false
        ORDER BY created_at DESC
    """
    )
    rows = cur.fetchall()
    cur.close()
    conn.close()
    candidates = []
    for row in rows:
        candidates.append(
            Candidate(
                id=str(row[0]),
                proj_id=str(row[1]),
                candidate_info=row[2],
                suggested_positions=row[3],
                analysis_status=row[4],
                last_matching=row[5],
                is_active=row[6],
                reason_info=row[7],
                updated_by=row[8],
                updated_at=row[9],
                created_by=row[10],
                created_at=row[11]
            )
        )
    return candidates


# Get candidates page
# This function retrieves a paginated list of candidates based on the search term and page number.
def get_candidates_page(page: int, chunk_size: int = 20, filters: Optional[CandidateFilters] = None) -> List[Candidate]:
    if page < 1:
        raise ValueError("Page number must be greater than or equal to 1.")

    if chunk_size < 1 or chunk_size > 1000:
        raise ValueError("Chunk size must be between 1 and 1000.")

    params = []
    offset = (page - 1) * chunk_size
    conn = psycopg2.connect(settings.DATABASE_URL)
    cur = conn.cursor()
    query = sql.SQL("""
        SELECT id, proj_id, candidate_info, suggested_positions, analysis_status, last_matching, is_active, reason_info, updated_by, updated_at, created_by, created_at
        FROM candidates_smarthr 
         {where_clause}
        ORDER BY created_at DESC
        LIMIT %s OFFSET %s
    """)

    # where_clause = sql.SQL("WHERE is_deleted = false")
    # params = [chunk_size, offset]
    # If filters are provided, build the WHERE clause dynamically
    where_clause, params = build_where_clause(filters)
    params.append(chunk_size)
    params.append(offset)
    query = query.format(where_clause=where_clause)

    candidates = []

    try:
        with psycopg2.connect(settings.DATABASE_URL) as conn:
            with conn.cursor() as cur:
                cur.execute(query, params)
                rows = cur.fetchall()
                for row in rows:
                    candidates.append(
                        Candidate(
                            id=str(row[0]),
                            proj_id=str(row[1]),
                            candidate_info=row[2],
                            suggested_positions=row[3],
                            analysis_status=row[4],
                            last_matching=row[5],
                            is_active=row[6],
                            reason_info=row[7],
                            updated_by=row[8],
                            updated_at=row[9],
                            created_by=row[10],
                            created_at=row[11]
                        )
                    )
    except Exception as e:
        raise e

    return candidates


# Get total candidates count
# This function retrieves the total count of candidates in the database that are not deleted.
def get_total_candidates(filters: Optional[CandidateFilters] = None) -> int:
    # params = []
    conn = psycopg2.connect(settings.DATABASE_URL)
    cur = conn.cursor()
    query = sql.SQL("""SELECT COUNT(*) FROM candidates_smarthr {where_clause}""")

    where_clause, params = build_where_clause(filters)

    query = query.format(where_clause=where_clause)

    cur.execute(query, params)

    row = cur.fetchone()
    cur.close()
    conn.close()
    if not row:
        return 0

    return row[0]


# Validate candidate
# This function validates a candidate against existing candidates in the database based on specified keys.
def validate_candidate(request: ValidateCandidateRequest) -> ValidateCandidateResponse:
    conn = None
    try:
        if not request.project_id:
            raise HTTPException(status_code=400, detail="No valid project_id provided.")
        conn = psycopg2.connect(settings.DATABASE_URL)
        with conn.cursor(cursor_factory=RealDictCursor) as cur:
            # Parse keys_to_eval and build dynamic SQL
            levenshtein_expressions = []
            params = []
            expressionParams = []

            candidate_dict = request.candidate_info

            for key_path in request.keys_to_eval:
                # Split key_path: e.g. "personal_info.full_name" -> ["personal_info", "full_name"]
                path_parts = key_path.split(".")
                # Construct the JSON path for PostgreSQL: '{personal_info,full_name}'
                json_path = "{" + ",".join(path_parts) + "}"

                # Extract value from candidate_dict using the path:
                # We'll do this programmatically to handle nested keys.
                current_val = candidate_dict
                for p in path_parts:
                    if p in current_val:
                        current_val = current_val[p]
                    else:
                        current_val = ""
                        break

                if not isinstance(current_val, str):
                    # If the value is not a string, convert it to string for comparison.
                    # If it's None or missing, treat as empty string.
                    current_val = str(current_val) if current_val is not None else ""

                expressionParams.append(current_val.lower())
                print("json_path", json_path)
                # Build the LEVENSHTEIN expression and parameter placeholder
                expression = f"levenshtein(c.candidate_info #>> '{json_path}', %s)"
                levenshtein_expressions.append(expression)
                print("expression", expression)
            # Add the corresponding candidate value to params
            params.extend(expressionParams)

            if not levenshtein_expressions:
                raise HTTPException(
                    status_code=400, detail="No valid keys to evaluate were provided."
                )

            # Build the average expression: sum of all distances divided by count
            avg_expression = f"({' + '.join(levenshtein_expressions)})::float / {len(levenshtein_expressions)}"
            print("avg_expression", avg_expression)
            # Now we build the full SQL query to find the closest matches from the database
            # We'll filter by project_id.
            sql_query = f"""
                SELECT
                    c.id,
                    {avg_expression} AS average_distance,
                    candidate_info
                FROM
                    candidates_smarthr c
                WHERE
                    c.is_deleted = false and c.proj_id = %s and ({avg_expression}) < 10
                ORDER BY
                    average_distance ASC
                LIMIT 4;
            """

            # Add project_id as the last parameter
            params.append(request.project_id)
            params.extend(expressionParams)

            cur.execute(sql_query, params)
            rows = cur.fetchall()

            # Extract the from the fetched rows
            matching_candidates = []

            for row in rows:
                matching_candidates.append(
                    ValidateCandidateItem(
                        id=str(row["id"]),
                        average_distance=row["average_distance"],
                        candidate_info=row["candidate_info"],
                    )
                )
            # The candidate_id can be something unique from the provided candidate_info

            response = ValidateCandidateResponse(
                matching_candidates=matching_candidates,
            )

            return response

    except HTTPException as he:
        print("Error", he)
        raise he
    except Exception as e:
        print("Error", e)
        raise HTTPException(status_code=500, detail=str(e))
    finally:
        if conn:
            conn.close()


# Get candidate embedding text based on project ID and candidate info
# This function generates a text representation of the candidate profile based on the project ID and candidate info
def get_candidate_embedding_text(proj_id: str, candidate_info: dict) -> str:
    # Implement your logic here
    ## Extract template based proj_id
    return f"Candidate Profile:\nName: {candidate_info.get('name', 'Unknown')}\nSkills: {', '.join(candidate_info.get('skills', []))}"


# Create a PDF file from the candidate data
def create_candidate_pdf(candidate: Candidate):
    template_path = os.path.join(os.getcwd(), 'templates', 'html', 'candidate_resume.html')
    print(template_path)
    logger.info("Getting html from path" + template_path)
    content = ""

    with open(template_path, 'r') as file:
        content = file.read()
        print(content)

    logger.info("content of HTML: " + content)
    template = Environment(loader=BaseLoader(), undefined=ChainableUndefined).from_string(content)
    templateOutput = template.render(candidate=candidate.candidate_info, current_date=datetime.now())

    print(templateOutput)
    logger.info("template Output: " + templateOutput)

    options = CANDIDATE_RESUME_PAGE_OPTIONS

    print(options)
    # logger.info("PDF Options : " + options)

    pdf_data = pdfkit.from_string(templateOutput, False, options=options)

    logger.info(f"Length of PDF Data : {str(len(pdf_data))}")

    return pdf_data


# Create a DOCX file from the candidate data
def create_candidate_docx(candidate: Candidate):
    try:
        output = io.BytesIO()

        candidate_pdf = create_candidate_pdf(candidate)
        file_stream = io.BytesIO(candidate_pdf)
        cv = Converter(stream=file_stream)

        cv.convert(output)
        cv.close()

        print(len(output.getbuffer()))

    except Exception as e:
        print(f"Error occurred while creating candidate: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

    return output.getbuffer()


# Change candidate status
def change_candidate_status(candidate_id: str, is_active: bool, reason_info: dict) -> Candidate:
    try:
        conn = psycopg2.connect(settings.DATABASE_URL)
        cur = conn.cursor()

        logger.info("creating connection to change candidate stattus")

        sqlQuery = """
            UPDATE candidates_smarthr SET
                is_active= %s,
                reason_info = %s, 
                updated_at = Now()
                Where id = %s
                RETURNING id, proj_id, candidate_info, suggested_positions, analysis_status, last_matching, is_active, reason_info, updated_by, updated_at, created_by, created_at
            """

        cur.execute(sqlQuery, (
                    is_active,
                    Json(reason_info),
                    candidate_id,
                    ))

        row = cur.fetchone()
        conn.commit()
        cur.close()
        conn.close()

        if not row:
            logger.info("Candidate not updated")
            return None

        logger.info("Candidate updated")

        return Candidate(
            id=str(row[0]),
            proj_id=str(row[1]),
            candidate_info=row[2],
            suggested_positions=row[3],
            analysis_status=row[4],
            last_matching=row[5],
            is_active=row[6],
            reason_info=row[7],
            updated_by=row[8],
            updated_at=row[9],
            created_by=row[10],
            created_at=row[11]
        )

    except Exception as e:
        print(f"Error occurred while changing candidate status: {str(e)}")
        logger.info(f"Error occurred while changing candidate status: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
    finally:
        if conn:
            conn.close()


# Delete candidate, set is_deleted = true, set reason_info
def delete_candidate(candidate_id: str, reason_info: dict) -> bool:
    try:
        conn = psycopg2.connect(settings.DATABASE_URL)
        cur = conn.cursor()
        sqlQuery = """
            UPDATE candidates_smarthr SET
                is_deleted= true,
                reason_info = %s, 
                updated_at = Now()
                Where id = %s
                RETURNING id, proj_id, candidate_info, suggested_positions, analysis_status, last_matching, is_active, reason_info, updated_by, updated_at, created_by, created_at
            """
        cur.execute(sqlQuery, (Json(reason_info), candidate_id),)
        conn.commit()
        cur.close()
        conn.close()
        return True
    except psycopg2.Error as e:
        print(f"Database error occurred while delete_candidate: {str(e)}")
        raise HTTPException(status_code=500, detail=f"delete_candidate. Database error occurred: {str(e)}")
    except HTTPException as he:
        print(f"HTTP error occurred while delete_candidate: {str(he.detail)}")
        raise he
    except Exception as e:
        print(f"Error occurred while delete_candidate: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
    finally:
        if conn:
            conn.close()


# Upload candidate from file
# { 'Content-Type': 'multipart/form-data' }
def upload_candidate_from_file(file: bytes, file_name: str) -> Candidate:
    try:
        # Read the file content
        file_content = file.decode('utf-8')
        # Parse the content as JSON
        candidate_data = json.loads(file_content)

        # Create a new candidate using the parsed data
        candidate = create_candidate(CandidateCreate(**candidate_data))

        return candidate

    except Exception as e:
        print(f"Error occurred while uploading candidate from file: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


# Get candidate by email
def get_candidate_by_email(email: str) -> Optional[Candidate]:
    try:
        if not email:
            raise HTTPException(status_code=400, detail="Email is required")

        # Validate email format
        if not isinstance(email, str) or len(email) == 0:
            raise HTTPException(status_code=400, detail="Invalid email format")
        conn = psycopg2.connect(settings.DATABASE_URL)
        cur = conn.cursor()
        cur.execute(
            """
            SELECT id, proj_id, candidate_info, suggested_positions, analysis_status, last_matching, is_active, reason_info, updated_by, updated_at, created_by, created_at
            FROM candidates_smarthr
            WHERE is_deleted = false
            AND EXISTS (
                SELECT 1
                FROM unnest(string_to_array(LOWER(TRIM(candidate_info #>> '{personal_info,email}')), ';')) AS email
                WHERE TRIM(email) = LOWER(TRIM(%s))
            )
        """,
            (email.lower(),),
        )
        row = cur.fetchone()
        cur.close()
        conn.close()

        if not row:
            return None

        return Candidate(
            id=str(row[0]),
            proj_id=str(row[1]),
            candidate_info=row[2],
            suggested_positions=row[3],
            analysis_status=row[4],
            last_matching=row[5],
            is_active=row[6],
            reason_info=row[7],
            updated_by=row[8],
            updated_at=row[9],
            created_by=row[10],
            created_at=row[11]
        )
    except Exception as e:
        print(f"Error occurred while fetching candidate by email: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


# Test LLM match logic
def test_llm_match_logic(model_name: str, position_id: str, candidate_id: str = None):
    """
    Logic to test LLM output for a single candidate match.
    """
    try:
        conn = psycopg2.connect(settings.DATABASE_URL)
        cur = conn.cursor()

        # Retrieve the position
        cur.execute(
            "SELECT to_be_embebbed, embedding FROM positions_smarthr WHERE id=%s",
            (position_id,),
        )
        position_row = cur.fetchone()
        if not position_row:
            cur.close()
            conn.close()
            raise HTTPException(status_code=404, detail="Position not found")

        processed_position = position_row[0]

        # logic if candidate_id is provided
        if candidate_id:
            try:
                logger.info("Candidate id provided: %s-----------------------\n", candidate_id)
                cur.execute(
                    "SELECT to_be_embebbed, embedding FROM candidates_smarthr WHERE id=%s",
                    (candidate_id,),
                )
                candidate_row = cur.fetchone()

                processed_candidate = candidate_row[0]

                # Perform the match using the specified model
                models_order = [model_name]
                # parallel simulation
                analysis_parallel = match_functions.evaluate_candidate(
                    processed_candidate, processed_position, models_order=models_order,
                )
                response_parallel = {"candidate_id": candidate_id, "position_id": position_id, "model_name": model_name, "analysis": dict(analysis_parallel)}
                # batch simulation
                analysis_batch = match_functions.evaluate_candidates_batch(
                    [processed_candidate], processed_position, models_order=models_order,
                )
                analysis_batch = analysis_batch.candidates_analysis[0]
                response_batch = {"candidate_id": candidate_id, "position_id": position_id, "model_name": model_name, "analysis": dict(analysis_batch)}
                return {"parallel": response_parallel, "batch": response_batch}
            except Exception as e:
                # log continuing without candidate_id
                logger.warning(f"Error occurred while fetching candidate by id: {str(e)}")

        position_embedding = position_row[1]

        # Find the most similar candidate
        cur.execute(
            """
            SELECT id, to_be_embebbed FROM candidates_smarthr
            WHERE is_deleted = false and is_active = true
            ORDER BY 1 - (embedding <=> %s) DESC
            LIMIT 1
            """,
            (position_embedding,),
        )
        candidate_row = cur.fetchone()
        if not candidate_row:
            cur.close()
            conn.close()
            raise HTTPException(status_code=404, detail="No candidates found")

        candidate_id = candidate_row[0]
        processed_candidate = candidate_row[1]

        cur.close()
        conn.close()

        # Perform the match using the specified model
        models_order = [model_name]

        analysis_parallel = match_functions.evaluate_candidate(
            processed_candidate, processed_position, models_order=models_order,
        )
        parallel_response = {"candidate_id": candidate_id, "position_id": position_id, "model_name": model_name, "analysis": dict(analysis_parallel)}

        analysis_batch = match_functions.evaluate_candidates_batch(
            [processed_candidate], processed_position, models_order=models_order,
        )
        analysis_batch = analysis_batch.candidates_analysis[0]
        batch_response = {"candidate_id": candidate_id, "position_id": position_id, "model_name": model_name, "analysis": dict(analysis_batch)}

        return {"parallel": parallel_response, "batch": batch_response}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# Get job titles from candidates
# This function retrieves distinct job titles from candidates' work experience.
def get_job_titles() -> List[str]:
    with get_cursor() as cur:
        cur.execute("""SELECT DISTINCT job_title
            FROM (
            SELECT 
                COALESCE(
                NULLIF(candidate_info->'work_experience'->0->>'job_title', ''),
                NULLIF(candidate_info->'work_experience'->1->>'job_title', ''),
                NULLIF(candidate_info->'work_experience'->2->>'job_title', ''),
                NULLIF(candidate_info->'work_experience'->3->>'job_title', ''),
                NULLIF(candidate_info->'work_experience'->4->>'job_title', '')
                ) AS job_title
            FROM public.candidates_smarthr
            WHERE is_deleted = false
            ) AS sub
            WHERE job_title IS NOT NULL""")
        rows = cur.fetchall()
    return [row[0] for row in rows]


# Get distinct countries from candidates
# This function retrieves a list of distinct countries from candidates' personal_info.
def get_countries() -> List[str]:
    with get_cursor() as cur:
        cur.execute("""
            SELECT DISTINCT country
            FROM (
            SELECT 
               NULLIF(candidate_info->'personal_info'->>'country', '')  AS country
            FROM public.candidates_smarthr
            WHERE is_deleted = false
            ) AS sub
            WHERE country IS NOT NULL
        """)
        rows = cur.fetchall()
    return [row[0] for row in rows]


# Build WHERE clause for filtering candidates
# This function constructs a WHERE clause based on the provided filters.
def build_where_clause(filters: CandidateFilters) -> sql.SQL:

    where_clause = sql.SQL("WHERE is_deleted = false")
    params = []

    if filters:
        # Check if filters are provided and build the WHERE clause dynamically
        if (len(filters.search_term.strip()) > 0):
            where_clause = sql.SQL("WHERE is_deleted = false and lower(candidate_info::text) LIKE %s")
            params.insert(0, f"%{filters.search_term.lower()}%")

        if filters.status is not None:
            where_clause += sql.SQL(" AND is_active = %s")
            params.append(filters.status)

        if filters.country:
            where_clause += sql.SQL(" AND candidate_info #>> '{personal_info,country}' = %s")
            params.append(filters.country)

        if filters.role:
            where_clause += sql.SQL(" AND EXISTS (SELECT 1 FROM jsonb_array_elements(candidate_info->'work_experience') AS we WHERE lower(we->>'job_title') LIKE %s)")
            params.append(f"%{filters.role.lower()}%")

        # Date filters
        # Assuming created_from and created_to are datetime objects
        if filters.created_from and filters.created_to:
            # Normalize created_from to 00:00:00
            start = datetime.combine(filters.created_from.date(), time.min)
            # Normalize created_to to 23:59:59.999999
            end = datetime.combine(filters.created_to.date(), time.max)
            where_clause += sql.SQL(" AND created_at BETWEEN %s AND %s")
            params.append(start)
            params.append(end)
        # if filters.created_from:
        #     where_clause += sql.SQL(" AND created_at >= %s")
        #     params.append(filters.created_from)

        # if filters.created_to:
        #     where_clause += sql.SQL(" AND created_at <= %s")
        #     params.append(filters.created_to)

    return where_clause, params
