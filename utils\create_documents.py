
# utils/create_documents.py
from reportlab.lib.pagesizes import letter
from reportlab.pdfgen import canvas
from reportlab.lib.colors import HexColor
from models.arroyo_models import ArroyoCandidate
import os

import io
from reportlab.lib.pagesizes import letter
from reportlab.lib.colors import HexColor
from reportlab.pdfgen import canvas
import textwrap

def create_pdf(candidate: ArroyoCandidate, filename: str) -> str:
    buffer = io.BytesIO()
    c = canvas.Canvas(buffer, pagesize=letter)
    width, height = letter
    margin = 50
    y_position = height - margin  # Starting position for content

    # Define brand colors
    primary_color = HexColor("#1F497D")  # Dark blue
    secondary_color = HexColor("#4F81BD")  # Light blue

    # Function to handle text wrapping
    def draw_text_with_wrapping(c, x, y, text, max_width):
        text_object = c.beginText(x, y)
        text_object.setFont("Helvetica", 10)
        text_object.setFillColor(primary_color)
        text_object.setLeading(12)
        for line in textwrap.wrap(text, width=max_width):
            text_object.textLine(line)
        c.drawText(text_object)
        return y - (len(textwrap.wrap(text, width=max_width)) * 12)

    # Function to check if a new page is needed
    def check_page_end(c, y_position, line_height=15):
        if y_position < margin:
            c.showPage()
            return height - margin
        return y_position

    # Title
    c.setFont("Helvetica-Bold", 16)
    c.setFillColor(primary_color)
    c.drawString(margin, y_position, "ARROYO Candidate Profile")
    y_position -= 30

    # Personal Information
    if candidate.personal_info:
        c.setFont("Helvetica-Bold", 12)
        c.setFillColor(secondary_color)
        c.drawString(margin, y_position, "Personal Information:")
        y_position -= 20
        c.setFont("Helvetica", 10)
        c.setFillColor(primary_color)
        for field, value in candidate.personal_info.dict().items():
            if value != "not_provided":
                c.drawString(margin + 20, y_position, f"{field.replace('_', ' ').title()}: {value}")
                y_position -= 15
                y_position = check_page_end(c, y_position)

    # Summary
    if candidate.summary:
        y_position -= 20
        c.setFont("Helvetica-Bold", 12)
        c.setFillColor(secondary_color)
        c.drawString(margin, y_position, "Summary:")
        y_position -= 20
        y_position = draw_text_with_wrapping(c, margin + 20, y_position, candidate.summary, max_width=80)
        y_position -= 20

    # Experience
    if candidate.experience:
        c.setFont("Helvetica-Bold", 12)
        c.setFillColor(secondary_color)
        c.drawString(margin, y_position, "Experience:")
        y_position -= 20
        for exp in candidate.experience:
            c.setFont("Helvetica", 10)
            c.setFillColor(primary_color)
            c.drawString(margin + 20, y_position, f"Company: {exp.company}")
            y_position -= 15
            c.drawString(margin + 20, y_position, f"Position: {exp.position}")
            y_position -= 15
            c.drawString(margin + 20, y_position, f"Start Date: {exp.start_date} - End Date: {exp.end_date or 'Present'}")
            y_position -= 15
            y_position = draw_text_with_wrapping(c, margin + 40, y_position, exp.description, max_width=60)
            y_position -= 20
            y_position = check_page_end(c, y_position)

    # Education
    if candidate.undergraduate_education.degree != "not_provided":
        c.setFont("Helvetica-Bold", 12)
        c.setFillColor(secondary_color)
        c.drawString(margin, y_position, "Undergraduate Education:")
        y_position -= 20
        c.setFont("Helvetica", 10)
        c.setFillColor(primary_color)
        c.drawString(margin + 20, y_position, f"{candidate.undergraduate_education.degree}, {candidate.undergraduate_education.institution} ({candidate.undergraduate_education.year})")
        y_position -= 20
        y_position = check_page_end(c, y_position)

    # Graduate Education
    if candidate.graduate_education:
        c.setFont("Helvetica-Bold", 12)
        c.setFillColor(secondary_color)
        c.drawString(margin, y_position, "Graduate Education:")
        y_position -= 20
        for grad in candidate.graduate_education:
            c.setFont("Helvetica", 10)
            c.setFillColor(primary_color)
            c.drawString(margin + 20, y_position, f"{grad.degree}, {grad.institution} ({grad.year})")
            y_position -= 15
            y_position = check_page_end(c, y_position)

    # Extra Courses
    if candidate.extra_courses:
        c.setFont("Helvetica-Bold", 12)
        c.setFillColor(secondary_color)
        c.drawString(margin, y_position, "Extra Courses:")
        y_position -= 20
        for course in candidate.extra_courses:
            c.setFont("Helvetica", 10)
            c.setFillColor(primary_color)
            c.drawString(margin + 20, y_position, f"Certification: {course.certification_name}")
            y_position -= 15
            y_position = check_page_end(c, y_position)

    # Technical Skills
    if candidate.technical_skills:
        c.setFont("Helvetica-Bold", 12)
        c.setFillColor(secondary_color)
        c.drawString(margin, y_position, "Technical Skills:")
        y_position -= 20
        skills_text = ", ".join(candidate.technical_skills)
        y_position = draw_text_with_wrapping(c, margin + 20, y_position, skills_text, max_width=100)
        y_position -= 20

    # Soft Skills
    if candidate.soft_skills:
        c.setFont("Helvetica-Bold", 12)
        c.setFillColor(secondary_color)
        c.drawString(margin, y_position, "Soft Skills:")
        y_position -= 20
        soft_skills_text = ", ".join(candidate.soft_skills)
        y_position = draw_text_with_wrapping(c, margin + 20, y_position, soft_skills_text, max_width=100)
        y_position -= 20

    # Finalize and save
    c.save()
    buffer.seek(0)
    return buffer.getvalue()

### Create Docx
from docx import Document
from docx.shared import RGBColor
from models.arroyo_models import ArroyoCandidate
import os
import io
BASE_DIR = '/app/files'

def create_docx(candidate: ArroyoCandidate, filename: str) -> str:
    doc = Document()

    # Define Arroyo Consulting's brand colors
    primary_color = RGBColor(31, 73, 125)  # Dark blue
    secondary_color = RGBColor(79, 129, 189)  # Light blue

    # Title
    title = doc.add_heading('ARROYO Candidate Profile', level=1)
    title.runs[0].font.color.rgb = primary_color

    # Personal Information
    if candidate.personal_info:
        heading = doc.add_heading('Personal Information:', level=2)
        heading.runs[0].font.color.rgb = secondary_color
        personal_info = candidate.personal_info
        for field, value in personal_info.dict().items():
            if value != "not_provided":
                p = doc.add_paragraph(f"{field.replace('_', ' ').title()}: {value}")
                p.runs[0].font.color.rgb = primary_color

    # Summary
    if candidate.summary:
        heading = doc.add_heading('Summary:', level=2)
        heading.runs[0].font.color.rgb = secondary_color
        p = doc.add_paragraph(candidate.summary)
        p.runs[0].font.color.rgb = primary_color

    # Experience
    if candidate.experience:
        heading = doc.add_heading('Experience:', level=2)
        heading.runs[0].font.color.rgb = secondary_color
        for exp in candidate.experience:
            p = doc.add_paragraph()
            p.add_run(f"Company: {exp.company}\n").bold = True
            p.add_run(f"Position: {exp.position}\n").bold = True
            p.add_run(f"Start Date: {exp.start_date} - End Date: {exp.end_date or 'Present'}\n").bold = True
            p.add_run(f"Description: {exp.description}")
            p.runs[0].font.color.rgb = primary_color

    # Education
    if candidate.undergraduate_education.degree != "not_provided":
        heading = doc.add_heading('Undergraduate Education:', level=2)
        heading.runs[0].font.color.rgb = secondary_color
        doc.add_paragraph(f"{candidate.undergraduate_education.degree}, {candidate.undergraduate_education.institution} ({candidate.undergraduate_education.year})")

    if candidate.graduate_education:
        heading = doc.add_heading('Graduate Education:', level=2)
        heading.runs[0].font.color.rgb = secondary_color
        for grad in candidate.graduate_education:
            doc.add_paragraph(f"{grad.degree}, {grad.institution} ({grad.year})")

    # Extra Courses
    if candidate.extra_courses:
        heading = doc.add_heading('Extra Courses:', level=2)
        heading.runs[0].font.color.rgb = secondary_color
        for course in candidate.extra_courses:
            doc.add_paragraph(f"Certification: {course.certification_name}")

    # Technical Skills
    if candidate.technical_skills:
        heading = doc.add_heading('Technical Skills:', level=2)
        heading.runs[0].font.color.rgb = secondary_color
        doc.add_paragraph(", ".join(candidate.technical_skills))

    # Soft Skills
    if candidate.soft_skills:
        heading = doc.add_heading('Soft Skills:', level=2)
        heading.runs[0].font.color.rgb = secondary_color
        doc.add_paragraph(", ".join(candidate.soft_skills))

        # Save DOCX to a BytesIO buffer
    buffer = io.BytesIO()
    doc.save(buffer)
    buffer.seek(0)
    return buffer.getvalue()