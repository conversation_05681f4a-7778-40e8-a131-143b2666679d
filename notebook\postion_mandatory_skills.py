from pydantic import BaseModel, Field
from typing import List, Optional


class ExplicitSkill(BaseModel):
    name: str = Field(..., description="One of the most important skills described in the skills section.")
    priority: int = Field(0, description="Number from 0 to 10 of importance")  # Default value

class InferencedSkill(BaseModel):
    name: str = Field(..., description="One of the most important skills inferred from the description.")
    priority: int = Field(0, description="Number from 0 to 10 of importance rebalanced by your consideration.")

class TopSkills(BaseModel):
    skills_from_explicit: List[ExplicitSkill] = Field(None, description="List of the top skills described explicitly.")
    skills_from_description: List[InferencedSkill] = Field(None, description="List of the top skills not setted as skill but infered from the description.")