import psycopg2
from psycopg2.extras import RealDictCursor
from typing import List, Dict

def get_positions() -> List[Dict]:
    try:
        connection = psycopg2.connect(
            user="arroyo_postgres_admin",
            password="@rr0y02024$",
            host="ai-db-poc-server.postgres.database.azure.com",
            port=5432,
            database="postgres",
        )
        cursor = connection.cursor(cursor_factory=RealDictCursor)  # This ensures the result is returned as a dict
        
        query = "SELECT * FROM positions;"  # Query to fetch all positions
        cursor.execute(query)
        positions = cursor.fetchall()  # Fetch all rows from the query
        
        return positions
    except Exception as e:
        print(f"Error fetching positions: {e}")
        return []
    finally:
        if connection:
            cursor.close()
            connection.close()

def get_total_candidates_plsql() -> int:
    try:
        connection = psycopg2.connect(
            user="arroyo_postgres_admin",
            password="@rr0y02024$",
            host="ai-db-poc-server.postgres.database.azure.com",
            port=5432,
            database="postgres",
        )
        cursor = connection.cursor()  # This ensures the result is returned as a dict
        
        query = "SELECT COUNT(*) FROM candidates"  # Query to fetch the total number of candidates
        cursor.execute(query)
        total_candidates = cursor.fetchone()[0]  # Fetch the first row from the query
        
        return total_candidates
    except Exception as e:
        print(f"Error fetching total candidates: {e}")
        return 0
    finally:
        if connection:
            cursor.close()
            connection.close()

def get_total_arroyo_candidates_plsql(search_term: str) -> int:
    try:
        connection = psycopg2.connect(
            user="arroyo_postgres_admin",
            password="@rr0y02024$",
            host="ai-db-poc-server.postgres.database.azure.com",
            port=5432,
            database="postgres",
        )
        cursor = connection.cursor()  # This ensures the result is returned as a dict
        
        query = "SELECT COUNT(*) FROM arroyo_candidates"  # Query to fetch the total number of arroyo_candidates

        if(len(search_term) > 0):
              query += f"""
                Where personal_info->>'city' like '%{search_term}%'
                OR personal_info->>'email' like '%{search_term}%%'
                OR personal_info->>'country' like '%{search_term}%%'
                OR personal_info->>'full_name' like '%{search_term}%%'
            """

        cursor.execute(query)
        total_candidates = cursor.fetchone()[0]  # Fetch the first row from the query
        
        return total_candidates
    except Exception as e:
        print(f"Error fetching total candidates: {e}")
        return 0
    finally:
        if connection:
            cursor.close()
            connection.close()

def get_positions_page(page: int, chunk_size: int = 10) -> List[Dict]:
    try:
        connection = psycopg2.connect(
            user="arroyo_postgres_admin",
            password="@rr0y02024$",
            host="ai-db-poc-server.postgres.database.azure.com",
            port=5432,
            database="postgres",
        )
        cursor = connection.cursor(cursor_factory=RealDictCursor)  # This ensures the result is returned as a dict
        
        query = "SELECT * FROM positions LIMIT %s OFFSET %s;"  # Query to fetch positions with pagination
        cursor.execute(query, (chunk_size, chunk_size * (page - 1)))
        positions = cursor.fetchall()  # Fetch all rows from the query
        
        return positions
    except Exception as e:
        print(f"Error fetching positions: {e}")
        return []
    finally:
        if connection:
            cursor.close()
            connection.close()

def get_position_by_id(position_id: str) -> Dict:
    try:
        connection = psycopg2.connect(
            user="arroyo_postgres_admin",
            password="@rr0y02024$",
            host="ai-db-poc-server.postgres.database.azure.com",
            port=5432,
            database="postgres",
        )
        cursor = connection.cursor(cursor_factory=RealDictCursor)  # This ensures the result is returned as a dict
        
        query = "SELECT * FROM positions WHERE id = %s;"  # Query to fetch a position by its ID
        cursor.execute(query, (position_id,))
        position = cursor.fetchone()  # Fetch the first row from the query
        
        return position
    except Exception as e:
        print(f"Error fetching position by ID: {e}")
        return {}
    finally:
        if connection:
            cursor.close()
            connection.close()
        
def get_candidate_by_id(candidate_id: int) -> Dict:
    try:
        connection = psycopg2.connect(
            user="arroyo_postgres_admin",
            password="@rr0y02024$",
            host="ai-db-poc-server.postgres.database.azure.com",
            port=5432,
            database="postgres",
        )
        cursor = connection.cursor(cursor_factory=RealDictCursor)  # This ensures the result is returned as a dict
        
        query = "SELECT * FROM candidates WHERE candidate_id = %s;"  # Query to fetch a candidate by its ID
        cursor.execute(query, (candidate_id,))
        candidate = cursor.fetchone()  # Fetch the first row from the query
        
        return candidate
    except Exception as e:
        print(f"Error fetching candidate by ID: {e}")
        return {}
    finally:
        if connection:
            cursor.close()
            connection.close()

def get_candidates_by_ids(candidate_ids: List[str]) -> List[Dict]:
    try:
        connection = psycopg2.connect(
            user="arroyo_postgres_admin",
            password="@rr0y02024$",
            host="ai-db-poc-server.postgres.database.azure.com",
            port=5432,
            database="postgres",
        )
        cursor = connection.cursor(cursor_factory=RealDictCursor)  # This ensures the result is returned as a dict
        
        query = "SELECT * FROM candidates WHERE candidate_id IN %s;"  # Query to fetch candidates by their IDs
        


        cursor.execute(query, (tuple(candidate_ids),))
        candidates = cursor.fetchall()  # Fetch all rows from the query
        #delete embedding_small3 and embedding_text from the candidates
        for candidate in candidates:
            del candidate['embedding_small3']
            del candidate['embedding_text']
            
        return candidates
    except Exception as e:
        print(f"Error fetching candidates by IDs: {e}")
        return []
    finally:
        if connection:
            cursor.close()
            connection.close()

import psycopg2
from psycopg2.extras import Json
from models.arroyo_models import ArroyoCandidate

# Database connection settings
DB_SETTINGS = {
    "user": "arroyo_postgres_admin",
    "password": "@rr0y02024$",
    "host": "ai-db-poc-server.postgres.database.azure.com",
    "port": 5432,
    "database": "postgres",
}

def ensure_table_and_column():
    """
    Ensures that the 'arroyo_candidates' table and the 'vector_embedding' column exist.
    If the table or column doesn't exist, they are created.
    """
    connection = psycopg2.connect(**DB_SETTINGS)
    cursor = connection.cursor()

    # Check if the 'arroyo_candidates' table exists
    cursor.execute("""
        SELECT EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_name = 'arroyo_candidates'
        );
    """)
    table_exists = cursor.fetchone()[0]

    if not table_exists:
        # Create the 'arroyo_candidates' table with 'vector_embedding' as a vector column
        cursor.execute("""
            CREATE TABLE arroyo_candidates (
                identifier TEXT PRIMARY KEY,
                personal_info JSONB,
                summary TEXT,
                experience JSONB,
                undergraduate_education JSONB,
                graduate_education JSONB,
                extra_courses JSONB,
                technical_skills JSONB,
                soft_skills JSONB,
                full_text TEXT,
                text_to_embed TEXT,
                vector_embedding VECTOR(1536),  -- Adjust 1536 to your embedding dimension
                created_date TIMESTAMP timestamp NOT NULL DEFAULT NOW()
            );
        """)
        print("Created 'arroyo_candidates' table with 'vector_embedding' column.")
    else:
        # Check if the 'vector_embedding' column exists
        cursor.execute("""
            SELECT EXISTS (
                SELECT FROM information_schema.columns 
                WHERE table_name = 'arroyo_candidates' 
                AND column_name = 'vector_embedding'
            );
        """)
        column_exists = cursor.fetchone()[0]

        if not column_exists:
            # Add the 'vector_embedding' column
            cursor.execute("""
                ALTER TABLE arroyo_candidates
                ADD COLUMN vector_embedding VECTOR(1536);  -- Adjust 1536 to your embedding dimension
            """)
            print("Added 'vector_embedding' column to 'arroyo_candidates' table.")

    # Check if the index on 'vector_embedding' exists
    cursor.execute("""
        SELECT EXISTS (
            SELECT FROM pg_indexes 
            WHERE tablename = 'arroyo_candidates' 
            AND indexname = 'vector_embedding_hnsw_idx'
        );
    """)
    index_exists = cursor.fetchone()[0]

    if not index_exists:
        # Create the hnsw index on 'vector_embedding' using pgvector's operator class for similarity search
        cursor.execute("""
            CREATE INDEX vector_embedding_hnsw_idx
            ON arroyo_candidates
            USING hnsw (vector_embedding vector_cosine_ops);
        """)
        print("Created 'hnsw' index on 'vector_embedding' column with 'vector_cosine_ops' operator class.")

    connection.commit()
    cursor.close()
    connection.close()

def insert_or_update_candidate_data(identifier: str, candidate: ArroyoCandidate, full_text: str,
                                    embedding_text: str, vector_embedding: list):
    """
    Inserts or updates a candidate's data in the 'arroyo_candidates' table.
    """
    # Ensure the table and column are present
    ensure_table_and_column()

    connection = psycopg2.connect(**DB_SETTINGS)
    cursor = connection.cursor()

    sql = """
        INSERT INTO arroyo_candidates (
            identifier, personal_info, summary, experience, undergraduate_education,
            graduate_education, extra_courses, technical_skills, soft_skills,
            full_text, text_to_embed, vector_embedding
        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        ON CONFLICT (identifier) DO UPDATE SET
            personal_info = EXCLUDED.personal_info,
            summary = EXCLUDED.summary,
            experience = EXCLUDED.experience,
            undergraduate_education = EXCLUDED.undergraduate_education,
            graduate_education = EXCLUDED.graduate_education,
            extra_courses = EXCLUDED.extra_courses,
            technical_skills = EXCLUDED.technical_skills,
            soft_skills = EXCLUDED.soft_skills,
            full_text = EXCLUDED.full_text,
            text_to_embed = EXCLUDED.text_to_embed,
            vector_embedding = EXCLUDED.vector_embedding;
    """

    values = (
        identifier,
        Json(candidate.personal_info.model_dump()),  # Use model_dump() to get a dict
        candidate.summary,
        Json([exp.model_dump() for exp in candidate.experience]),
        Json(candidate.undergraduate_education.model_dump()),
        Json([grad.model_dump() for grad in candidate.graduate_education]),
        Json([course.model_dump() for course in candidate.extra_courses]),
        Json(candidate.technical_skills),
        Json(candidate.soft_skills),
        full_text,
        embedding_text,
        vector_embedding
    )

    cursor.execute(sql, values)
    connection.commit()
    cursor.close()
    connection.close()