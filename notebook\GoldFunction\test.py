from tansformation import inference_with_fallback
from position_output_class import Position
from langchain_core.messages import HumanMessage, SystemMessage
import inspect
from pydantic import BaseModel,

user_message = """
[Position Raw]
'\nClient: BGSF\n\nPosition Name: Azure Data Engineer - BGSF - NSI\n\nJob Description: <p>We are seeking a talented and experienced Azure Data Engineer to join our team. The candidate will have a strong background in data engineering, Azure Data Factory, Azure Synapse, along with experience ingesting and consolidating data from various data sources including multiple ERP systems. This role requires expertise in designing and implementing data pipelines, data transformation, and data warehousing solutions using Azure services. The Senior Azure Data Engineer will play a crucial role in ensuring the security, reliability, and performance of our data infrastructure while collaborating with cross-functional teams to meet business objectives.</p> <p>• 5+ years of work experience in desig, develop, and troubleshoot complex SQL queries, on DBMS solutions such as Snowflake, Oracle, MS SQL Server, PostgreSQL, and MySQL</p> <p>• 3+ years of hands-on exp. in ETL/ELT tools</p> <p>• 3+ years of work exp. with Data Platforms</p> <p>• 1+ year development exp. in Python or similar scripting lang. for data automation</p> \n\nMain Responsibilities: <ul> <li>Collaborate with stakeholders to understand data requirements and design and maintain secure and compliant data processing pipelines using ADF, Azure Synapse, and other tools.</li> <li>Ingest data from various sources such as databases, files, APIs, and streaming platforms into Azure, ensuring efficient and reliable data transfer.</li> <li>Successful data movements and transformations using various file formats such as Delta, Parquet, Avro, and CSV.</li> <li>Implement and optimize data warehousing solutions using Azure Synapse SQL and Azure Synapse Analytics to support large-scale analytics and reporting.</li> <li>Collaborate with data scientists and analysts to design and implement data models for analytical workloads using Azure AS or Power BI.</li> <li>Implement DevOps and automation practices to automate data pipelines, monitor data processes, and manage deployments using Azure DevOps, Azure Monitor, and Azure Automation.</li> <li>Support and Troubleshoot On Premise gateways</li> </ul> \n\nOpen Position Create: 2024-04-17 15:57:01,\n\nStart Date: 2024-05-08 06:50:38,\n\nClose Date: 2024-05-09 21:46:10,\n\nReason: Closed by applicant assigned from other vendor,\n\nSkills: [{"SkillType":"positions.skillCategories.professionalSkills","Skills":[{"Skill":"Azure Data Factory","SkillLevel":"Upper Intermediate","Score":4},{"Skill":"Azure Synapse","SkillLevel":"Upper Intermediate","Score":4},{"Skill":"Python","SkillLevel":"Lower Intermediate","Score":2},{"Skill":"SnowFlake","SkillLevel":"Intermediate","Score":3},{"Skill":"SQL","SkillLevel":"Upper Intermediate","Score":4},{"Skill":"ETL Tools","SkillLevel":"Intermediate","Score":3},{"Skill":"PowerShell","SkillLevel":"Lower Intermediate","Score":2},{"Skill":"Azure DevOps","SkillLevel":"Intermediate","Score":3},{"Skill":"Azure Monitor","SkillLevel":"Intermediate","Score":3},{"Skill":"Azure Automation","SkillLevel":"Intermediate","Score":3},{"Skill":"ELT Tools","SkillLevel":"Intermediate","Score":3},{"Skill":"MSSQL Server","SkillLevel":"Intermediate","Score":3}]},{"SkillType":"positions.skillCategories.niceToHave","Skills":[{"Skill":"Azure Solutions Architect Expert","SkillLevel":"Intermediate","Score":3},{"Skill":"Azure DevOps Engineer Expert","SkillLevel":"Intermediate","Score":3},{"Skill":"Azure Databricks","SkillLevel":"Intermediate","Score":3},{"Skill":"Azure Analysis Services","SkillLevel":"Intermediate","Score":3},{"Skill":"Microsoft Fabric","SkillLevel":"Lower Intermediate","Score":2},{"Skill":"Informatica Data Quality","SkillLevel":"Lower Intermediate","Score":2},{"Skill":"PostgreSQL","SkillLevel":"Intermediate","Score":3},{"Skill":"MySQL","SkillLevel":"Intermediate","Score":3}]},{"SkillType":"positions.skillCategories.softSkills","Skills":[{"Skill":"Team Work","SkillLevel":"Intermediate","Score":3}]},{"SkillType":"positions.skillCategories.languages","Skills":[{"Skill":"English","SkillLevel":"B2 Upper Intermediate","Score":4}]}]\n\n'   
"""
user_message = [HumanMessage(user_message)]

def get_related_class_definitions(cls, visited=None):
    if visited is None:
        visited = set()
    source_code = inspect.getsource(cls)
    visited.add(cls)
    for field in cls.__annotations__.values():
        # Check if the field type is a class that inherits from BaseModel
        if isinstance(field, type) and issubclass(field, BaseModel) and field not in visited:
            source_code += "\n\n" + get_related_class_definitions(field, visited)
        elif getattr(field, '__origin__', None) is list:
            # Handle lists of BaseModel subclasses
            inner_type = field.__args__[0]
            if isinstance(inner_type, type) and issubclass(inner_type, BaseModel) and inner_type not in visited:
                source_code += "\n\n" + get_related_class_definitions(inner_type, visited)
    return source_code

result = inference_with_fallback(
    task_prompt="Extract all the position information",
    model_schema=Position,
    user_messages=user_message,
    model_schema_text= inspect.getsource(Position),
    models_order= ["gpt-4o","llama33-70b"]
)

print(result.m)